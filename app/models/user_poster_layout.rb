class UserPosterLayout < ApplicationRecord
  has_paper_trail
  belongs_to :entity, polymorphic: true
  belongs_to :rm_user, class_name: "Admin<PERSON>ser", foreign_key: "rm_user_id", optional: true
  has_many :user_leader_photos, dependent: :destroy
  has_many :layout_remarks, class_name: "PosterLayoutRemark", dependent: :destroy
  has_one :se_user, primary_key: 'se_user_id', class_name: "AdminUser", foreign_key: "id"
  enum h1_background_type: { transparent: "transparent", creative_based: "creative_based" }, _suffix: true
  enum h2_background_type: { transparent: "transparent", creative_based: "creative_based", sticker: "sticker" }, _suffix: true

  validates_presence_of :h1_count, :h2_count

  accepts_nested_attributes_for :user_leader_photos, allow_destroy: true
  attr_accessor :user_poster_photo, :is_subscribed, :user_poster_photo_with_background, :premium_package,
                :gold_white_flat_identity, :gold_curved_identity, :party_neon_curved_identity, :duration_in_months,
                :total_amount, :discount_amount, :payable_amount, :referred_by, :maintenance_amount,
                :poster_affiliated_party_id, :family_frame_photo, :family_frame_name, :hero_frame_photo,
                :custom_role_name, :update_from_admin_dashboard

  validate :check_layout_is_supported
  after_commit :update_user_poster_affiliated_party_id, :update_user_profile_photo, :send_user_to_mixpanel,
               :trigger_crm_approval_flow, :trigger_crm_rejection_flow, :send_layout_created_mixpanel_event
  after_create_commit :create_frame_recommendations,
                      :trigger_juspay_customer_creation, :set_trial_start_and_wati_campaign_key, :set_send_wati_msg_key,
                      :queue_poster_content_generation
  validate :validate_user_status

  enum review_status: {
    accepted: "accepted",
    rejected: "rejected",
    awaited: "awaited"
  }, _suffix: true

  def trigger_crm_approval_flow
    return unless entity.is_a?(User) && entity.status == 'active'

    if saved_change_to_review_status? && accepted_review_status?
      Metadatum.find_by(entity: self, key: Constants.layout_rejection_reason)&.destroy
      pp = entity.premium_pitch
      pp.enabled_trial! if pp&.may_enabled_trial?
    end
  end

  def trigger_crm_rejection_flow
    return unless entity.is_a?(User) && entity.status == 'active'

    if saved_change_to_review_status? && rejected_review_status?
      pp = entity.premium_pitch
      pp.layout_rejected! if pp&.may_layout_rejected?
    end
  end

  def trigger_juspay_customer_creation
    entity.get_juspay_customer_id if entity.is_a?(User)
  end

  def validate_user_status
    return unless entity.is_a?(User) && entity.status != 'active'
    errors.add(:entity_id, 'User must be in active status')
  end

  def check_layout_is_supported
    layout_type = "layout_#{h1_count}_#{h2_count}".to_sym
    if UserPosterLayout::LAYOUTS.keys.exclude?(layout_type)
      errors.add(:base, "Invalid layout type: #{layout_type}, valid layout types are: #{UserPosterLayout::LAYOUTS.keys}")
    end
  end

  def self.ransackable_associations(_auth_object = nil)
    []
  end

  LAYOUTS = {
    layout_0_0: {
      party_icon_position: "top",
      photos: []
    },
    layout_0_1: {
      party_icon_position: "top",
      photos: [{
                 type: :header_2,
                 position_x: 486.65,
                 position_y: 37.1,
                 radius: 53.41
               }]
    },
    layout_0_2: {
      party_icon_position: "top",
      photos: [{
                 type: :header_2,
                 position_x: 369.65,
                 position_y: 37.1,
                 radius: 53.41
               },
               {
                 type: :header_2,
                 position_x: 486.65,
                 position_y: 37.1,
                 radius: 53.41
               }]
    },
    layout_0_3: {
      party_icon_position: "top",
      photos: [
        {
          type: :header_2,
          position_x: 252.65,
          position_y: 37.1,
          radius: 53.41
        },
        {
          type: :header_2,
          position_x: 369.65,
          position_y: 37.1,
          radius: 53.41
        }, {
          type: :header_2,
          position_x: 486.65,
          position_y: 37.1,
          radius: 53.41
        }]
    },
    layout_0_4: {
      party_icon_position: "top",
      photos: [
        {
          type: :header_2,
          position_x: 233.24,
          position_y: 40.83,
          radius: 40.18
        },
        {
          type: :header_2,
          position_x: 325.24,
          position_y: 40.83,
          radius: 40.18
        },
        {
          type: :header_2,
          position_x: 417.24,
          position_y: 40.83,
          radius: 40.18
        },
        {
          type: :header_2,
          position_x: 509.24,
          position_y: 40.83,
          radius: 40.18
        }]
    },
    layout_0_5: {
      party_icon_position: "top",
      photos: [
        {
          type: :header_2,
          position_x: 325.24,
          position_y: 40.83,
          radius: 40.18
        },
        {
          type: :header_2,
          position_x: 417.24,
          position_y: 40.83,
          radius: 40.18
        },
        {
          type: :header_2,
          position_x: 509.24,
          position_y: 40.83,
          radius: 40.18
        },
        {
          type: :header_2,
          position_x: 371.24,
          position_y: 123.83,
          radius: 40.18
        },
        {
          type: :header_2,
          position_x: 463.24,
          position_y: 123.83,
          radius: 40.18
        }]
    },
    layout_0_6: {
      party_icon_position: "top",
      photos: [
        {
          type: :header_2,
          position_x: 279.24,
          position_y: 40.83,
          radius: 40.18
        },
        {
          type: :header_2,
          position_x: 371.24,
          position_y: 40.83,
          radius: 40.18
        },
        {
          type: :header_2,
          position_x: 463.24,
          position_y: 40.83,
          radius: 40.18
        },
        {
          type: :header_2,
          position_x: 325.24,
          position_y: 123.83,
          radius: 40.18
        },
        {
          type: :header_2,
          position_x: 417.24,
          position_y: 123.83,
          radius: 40.18
        },
        {
          type: :header_2,
          position_x: 509.24,
          position_y: 123.83,
          radius: 40.18
        }]
    },
    layout_0_7: {
      party_icon_position: "top",
      photos: [
        {
          type: :header_2,
          position_x: 233.24,
          position_y: 40.83,
          radius: 40.18
        },
        {
          type: :header_2,
          position_x: 325.24,
          position_y: 40.83,
          radius: 40.18
        },
        {
          type: :header_2,
          position_x: 417.24,
          position_y: 40.83,
          radius: 40.18
        },
        {
          type: :header_2,
          position_x: 509.24,
          position_y: 40.83,
          radius: 40.18
        },
        {
          type: :header_2,
          position_x: 279.24,
          position_y: 123.83,
          radius: 40.18
        },
        {
          type: :header_2,
          position_x: 371.24,
          position_y: 123.83,
          radius: 40.18
        },
        {
          type: :header_2,
          position_x: 463.24,
          position_y: 123.83,
          radius: 40.18
        }]
    },
    layout_0_8: {
      party_icon_position: "top",
      photos: [
        {
          type: :header_2,
          position_x: 241.24,
          position_y: 32.83,
          radius: 40.18
        },
        {
          type: :header_2,
          position_x: 333.24,
          position_y: 32.83,
          radius: 40.18
        },
        {
          type: :header_2,
          position_x: 425.24,
          position_y: 32.83,
          radius: 40.18
        },
        {
          type: :header_2,
          position_x: 517.24,
          position_y: 32.83,
          radius: 40.18
        },
        {
          type: :header_2,
          position_x: 207.24,
          position_y: 115.83,
          radius: 40.18
        },
        {
          type: :header_2,
          position_x: 299.24,
          position_y: 115.83,
          radius: 40.18
        },
        {
          type: :header_2,
          position_x: 391.24,
          position_y: 115.83,
          radius: 40.18
        },
        {
          type: :header_2,
          position_x: 483.24,
          position_y: 115.83,
          radius: 40.18
        }]
    },
    layout_0_9: {
      party_icon_position: "top",
      photos: [{
                 type: :header_2,
                 position_x: 140.24,
                 position_y: 40.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 232.24,
                 position_y: 40.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 324.24,
                 position_y: 40.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 416.24,
                 position_y: 40.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 508.24,
                 position_y: 40.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 186.24,
                 position_y: 123.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 278.24,
                 position_y: 123.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 370.24,
                 position_y: 123.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 462.24,
                 position_y: 123.83,
                 radius: 40.18
               }]
    },
    layout_0_10: {
      party_icon_position: "top",
      photos: [{
                 type: :header_2,
                 position_x: 140.24,
                 position_y: 40.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 232.24,
                 position_y: 40.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 324.24,
                 position_y: 40.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 416.24,
                 position_y: 40.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 508.24,
                 position_y: 40.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 106.24,
                 position_y: 123.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 198.24,
                 position_y: 123.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 290.24,
                 position_y: 123.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 382.24,
                 position_y: 123.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 474.24,
                 position_y: 123.83,
                 radius: 40.18
               }]
    },
    layout_1_0: {
      party_icon_position: "top",
      photos: [{
                 type: :header_1,
                 position_x: 416,
                 position_y: 40,
                 radius: 87.00
               }]
    },
    layout_1_1: {
      party_icon_position: "top",
      photos: [{
                 type: :header_1,
                 position_x: 416,
                 position_y: 40,
                 radius: 87.00
               },
               {
                 type: :header_2,
                 position_x: 296.65,
                 position_y: 64.1,
                 radius: 53.41
               }]
    },
    layout_1_2: {
      party_icon_position: "top",
      photos: [{
                 type: :header_1,
                 position_x: 416,
                 position_y: 40,
                 radius: 87.00
               },
               {
                 type: :header_2,
                 position_x: 179.65,
                 position_y: 64.1,
                 radius: 53.41
               },
               {
                 type: :header_2,
                 position_x: 296.65,
                 position_y: 64.1,
                 radius: 53.41
               }]
    },
    layout_1_3: {
      party_icon_position: "top",
      photos: [{
                 type: :header_1,
                 position_x: 424,
                 position_y: 32,
                 radius: 87.00
               },
               {
                 type: :header_2,
                 position_x: 149.24,
                 position_y: 40.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 241.24,
                 position_y: 40.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 333.24,
                 position_y: 40.83,
                 radius: 40.18
               }
      ]
    },
    layout_1_4: {
      party_icon_position: "left",
      photos: [{
                 type: :header_1,
                 position_x: 415,
                 position_y: 40,
                 radius: 87.50
               },
               {
                 type: :header_2,
                 position_x: 43.24,
                 position_y: 61.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 135.24,
                 position_y: 61.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 227.24,
                 position_y: 61.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 319.24,
                 position_y: 61.83,
                 radius: 40.18
               }
      ]
    },
    layout_1_5: {
      party_icon_position: "top",
      photos: [{
                 type: :header_1,
                 position_x: 415,
                 position_y: 40,
                 radius: 87.50
               },
               {
                 type: :header_2,
                 position_x: 145.24,
                 position_y: 40.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 237.24,
                 position_y: 40.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 329.24,
                 position_y: 40.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 191.24,
                 position_y: 123.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 283.24,
                 position_y: 123.83,
                 radius: 40.18
               }
      ]
    },
    layout_1_6: {
      party_icon_position: "top",
      photos: [{
                 type: :header_1,
                 position_x: 423,
                 position_y: 34,
                 radius: 87.50
               },
               {
                 type: :header_2,
                 position_x: 147.24,
                 position_y: 40.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 239.24,
                 position_y: 40.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 331.24,
                 position_y: 40.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 109.24,
                 position_y: 123.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 201.24,
                 position_y: 123.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 293.24,
                 position_y: 123.83,
                 radius: 40.18
               }
      ]
    },
    layout_2_0: {
      party_icon_position: "left",
      photos: [{
                 type: :header_1,
                 position_x: 40,
                 position_y: 40,
                 radius: 87
               },
               {
                 type: :header_1,
                 position_x: 416,
                 position_y: 40,
                 radius: 87
               }
      ]
    },
    layout_2_1: {
      party_icon_position: "left",
      photos: [{
                 type: :header_1,
                 position_x: 40,
                 position_y: 40,
                 radius: 87
               },
               {
                 type: :header_1,
                 position_x: 416,
                 position_y: 40,
                 radius: 87
               },
               {
                 type: :header_2,
                 position_x: 256.65,
                 position_y: 73.1,
                 radius: 53.41
               }
      ]
    },
    layout_2_2: {
      party_icon_position: "left",
      photos: [{
                 type: :header_1,
                 position_x: 32,
                 position_y: 32,
                 radius: 75
               },
               {
                 type: :header_1,
                 position_x: 448,
                 position_y: 32,
                 radius: 75
               },
               {
                 type: :header_2,
                 position_x: 207.65,
                 position_y: 54.1,
                 radius: 53.41
               },
               {
                 type: :header_2,
                 position_x: 324.65,
                 position_y: 54.1,
                 radius: 53.41
               }
      ]
    },
    layout_2_3: {
      party_icon_position: "left",
      photos: [{
                 type: :header_1,
                 position_x: 32,
                 position_y: 32,
                 radius: 75
               },
               {
                 type: :header_1,
                 position_x: 448,
                 position_y: 32,
                 radius: 75
               },
               {
                 type: :header_2,
                 position_x: 187.24,
                 position_y: 40.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 275.24,
                 position_y: 40.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 363.24,
                 position_y: 40.83,
                 radius: 40.18
               }
      ]
    },
    layout_2_4: {
      party_icon_position: "left",
      photos: [{
                 type: :header_1,
                 position_x: 32,
                 position_y: 32,
                 radius: 75
               },
               {
                 type: :header_1,
                 position_x: 448,
                 position_y: 32,
                 radius: 75
               },
               {
                 type: :header_2,
                 position_x: 247.24,
                 position_y: 48.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 339.24,
                 position_y: 48.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 211.24,
                 position_y: 131.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 303.24,
                 position_y: 131.83,
                 radius: 40.18
               }
      ]
    },
    layout_2_5: {
      party_icon_position: "left",
      photos: [{
                 type: :header_1,
                 position_x: 32,
                 position_y: 32,
                 radius: 75
               },
               {
                 type: :header_1,
                 position_x: 448,
                 position_y: 32,
                 radius: 75
               },
               {
                 type: :header_2,
                 position_x: 189.24,
                 position_y: 41.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 275.24,
                 position_y: 41.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 361.24,
                 position_y: 41.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 232.24,
                 position_y: 125.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 318.24,
                 position_y: 125.83,
                 radius: 40.18
               }
      ]
    },
    layout_2_6: {
      party_icon_position: "left",
      photos: [{
                 type: :header_1,
                 position_x: 32,
                 position_y: 32,
                 radius: 75
               },
               {
                 type: :header_1,
                 position_x: 448,
                 position_y: 32,
                 radius: 75
               },
               {
                 type: :header_2,
                 position_x: 185.24,
                 position_y: 47.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 269.24,
                 position_y: 47.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 353.24,
                 position_y: 47.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 209.24,
                 position_y: 130.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 293.24,
                 position_y: 130.83,
                 radius: 40.18
               },
               {
                 type: :header_2,
                 position_x: 377.24,
                 position_y: 130.83,
                 radius: 40.18
               }
      ]
    },
    layout_2_7: {
      party_icon_position: "left",
      photos: [{
                 type: :header_1,
                 position_x: 38,
                 position_y: 43,
                 radius: 70
               },
               {
                 type: :header_1,
                 position_x: 457,
                 position_y: 43,
                 radius: 70
               },
               {
                 type: :header_2,
                 position_x: 202.06,
                 position_y: 47.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 279.06,
                 position_y: 47.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 356.06,
                 position_y: 47.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 169.06,
                 position_y: 131.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 244.06,
                 position_y: 131.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 319.06,
                 position_y: 131.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 394.06,
                 position_y: 131.71,
                 radius: 34.3
               }
      ]
    },
    layout_2_8: {
      party_icon_position: "left",
      photos: [{
                 type: :header_1,
                 position_x: 34,
                 position_y: 65,
                 radius: 65
               },
               {
                 type: :header_1,
                 position_x: 460,
                 position_y: 65,
                 radius: 65
               },
               {
                 type: :header_2,
                 position_x: 163.06,
                 position_y: 59.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 240.06,
                 position_y: 59.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 317.06,
                 position_y: 59.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 394.06,
                 position_y: 59.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 163.06,
                 position_y: 140.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 240.06,
                 position_y: 140.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 317.06,
                 position_y: 140.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 394.06,
                 position_y: 140.71,
                 radius: 34.3
               }
      ]
    },
    layout_2_9: {
      party_icon_position: "left",
      photos: [{
                 type: :header_1,
                 position_x: 39,
                 position_y: 51,
                 radius: 60
               },
               {
                 type: :header_1,
                 position_x: 472,
                 position_y: 51,
                 radius: 60
               },
               {
                 type: :header_2,
                 position_x: 165.06,
                 position_y: 79.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 242.06,
                 position_y: 79.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 319.06,
                 position_y: 79.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 396.06,
                 position_y: 79.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 131.06,
                 position_y: 155.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 206.06,
                 position_y: 155.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 281.06,
                 position_y: 155.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 356.06,
                 position_y: 155.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 431.06,
                 position_y: 155.71,
                 radius: 34.3
               }
      ]
    },
    layout_2_10: {
      party_icon_position: "left",
      photos: [{
                 type: :header_1,
                 position_x: 50,
                 position_y: 42,
                 radius: 55
               },
               {
                 type: :header_1,
                 position_x: 477,
                 position_y: 42,
                 radius: 55
               },
               {
                 type: :header_2,
                 position_x: 165.06,
                 position_y: 79.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 242.06,
                 position_y: 79.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 319.06,
                 position_y: 79.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 396.06,
                 position_y: 79.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 93.06,
                 position_y: 155.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 168.06,
                 position_y: 155.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 243.06,
                 position_y: 155.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 318.06,
                 position_y: 155.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 393.06,
                 position_y: 155.71,
                 radius: 34.3
               },
               {
                 type: :header_2,
                 position_x: 468.06,
                 position_y: 155.71,
                 radius: 34.3
               }
      ]
    },
    layout_3_0: {
      party_icon_position: "left",
      photos: [{
                 type: :header_1,
                 position_x: 82,
                 position_y: 32,
                 radius: 75
               },
               {
                 type: :header_1,
                 position_x: 240,
                 position_y: 32,
                 radius: 75
               },
               {
                 type: :header_1,
                 position_x: 398,
                 position_y: 32,
                 radius: 75
               }
      ]
    }
  }

  def create_frame_recommendations
    return unless entity.is_a?(User)

    # create frame recommendations for user after poster layout is created
    entity.create_frame_recommendations
  end

  def update_user_poster_affiliated_party_id
    # Only run if the entity is a User and the update is coming from dashboard.
    return unless entity.is_a?(User)
    return unless @update_from_admin_dashboard

    # update user poster affiliated party id
    entity.update_poster_affiliated_party_id(@poster_affiliated_party_id)
  end

  def send_layout_created_mixpanel_event
    return unless entity.is_a?(User)
    # we are using first_activated_at to determine if this is the first time the layout is activated or not
    # to avoid sending the event multiple times when the layout is activated again
    if active? && saved_change_to_first_activated_at? && saved_change_to_first_activated_at[0].nil?
      creator_id = self.versions.first&.whodunnit
      creator_email = AdminUser.find_by_id(creator_id)&.email if creator_id.present?
      EventTracker.perform_async(entity_id, "layout_created", {
        "h1_count" => h1_count,
        "h2_count" => h2_count,
        "layout_status" => entity.user_poster_layouts.count == 1 ? "first" : "latest",
        "is_self_trial_user" => entity.self_trial_user?,
        "creator" => creator_email || "",
        "lead_admin_user_email" => se_user&.email || "", # lead_admin_user_email is se_user_email
        "lead_admin_user_id" => se_user_id, # lead_admin_user_id is se_user_id
        "lead_type": entity.premium_pitch&.lead_type
      })
    end
  end

  def set_trial_start_and_wati_campaign_key
    return unless entity.is_a?(User)
    return if entity.show_trial_start? || entity.has_ever_been_in_trial?
    # set show_trial_start_key and trail_activation_wati_key for user
    entity.set_user_metadatum(Constants.trial_activation_wati_campaign_count)
    entity.set_user_metadatum(Constants.yet_to_start_trial_key)
  end

  def update_user_profile_photo
    return unless entity.is_a?(User) && entity.photo.nil?

    upl = UserPosterLayout.where(entity: entity, active: true).last

    return unless upl.present? && entity.poster_photo_with_background.present?

    new_photo = Photo.new(url: entity.poster_photo_with_background.url,
                          user_id: entity.id,
                          explicit: false,
                          service: 'aws')

    entity.update(photo: new_photo) if new_photo.save
  end

  def send_user_to_mixpanel
    return unless entity.is_a?(User)

    # if active status changed
    if saved_change_to_active?
      SyncMixpanelUser.perform_async(entity_id)
    end
  end

  def set_send_wati_msg_key
    return unless entity.is_a?(User)
    return if SubscriptionUtils.has_user_ever_subscribed?(entity.id)

    # set send_wati_msg_key for user
    entity.set_send_wati_msg_key
  end

  def get_latest_remark_of_layout
    layout_remarks.last&.remarks
  end

  def queue_poster_content_generation
    return unless entity.is_a?(User)

    # Queue the comprehensive poster content generation worker
    # This will generate user video frames, identity photos, and protocol photos
    GenerateUserPosterContentWorker.perform_async(id)

    Rails.logger.info("Queued poster content generation for user #{entity_id}, layout #{id}")
  end
end
