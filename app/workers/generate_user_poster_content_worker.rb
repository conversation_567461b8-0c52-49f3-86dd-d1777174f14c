# frozen_string_literal: true

class GenerateUserPosterContentWorker
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker
  include Capture
  
  sidekiq_options queue: :video_posters_generation, retry: 3, lock: :until_and_while_executing, on_conflict: :log
  
  sidekiq_retries_exhausted do |msg, ex|
    Honeybadger.notify(ex, context: { args: msg["args"] })
    Rails.logger.error("GenerateUserPosterContentWorker retries exhausted: #{ex.message}")
  end

  def perform(user_poster_layout_id)
    Honeybadger.context({ user_poster_layout_id: user_poster_layout_id })
    
    user_poster_layout = UserPosterLayout.find_by_id(user_poster_layout_id)
    return if user_poster_layout.blank?
    
    # Only process User entities, not Circle entities
    return unless user_poster_layout.entity.is_a?(User)
    
    user = user_poster_layout.entity
    return if user.blank?

    Rails.logger.info("Starting poster content generation for user #{user.id}, layout #{user_poster_layout_id}")
    
    # Get all active video frames
    video_frames = VideoFrame.where(active: true)
    return if video_frames.empty?
    
    # Generate protocol photo first (shared across all video frames)
    protocol_photo_url = generate_protocol_photo(user)
    
    # Update the poster layout with protocol photo URL
    user_poster_layout.update!(video_frame_protocol_photo_url: protocol_photo_url) if protocol_photo_url.present?
    
    # Generate user video frames and identity photos for each video frame type
    generated_frames = []
    video_frames.each do |video_frame|
      begin
        # Generate new user video frame with identity photos
        user_video_frame = generate_user_video_frame_with_identity(user, video_frame)
        generated_frames << user_video_frame if user_video_frame.present?
        
        Rails.logger.info("Generated content for user #{user.id}, video_frame #{video_frame.id}")
        
      rescue StandardError => e
        # If generation fails for this frame, log error but continue with other frames
        Honeybadger.notify(e, context: { user_id: user.id, video_frame_id: video_frame.id, user_poster_layout_id: user_poster_layout_id })
        Rails.logger.error("Failed to generate content for user #{user.id}, video_frame #{video_frame.id}: #{e.message}")
      end
    end
    
    # Zero-downtime approach: All frames are now active and updated
    if generated_frames.any?
      Rails.logger.info("Successfully generated/updated #{generated_frames.count} video frames for user #{user.id}")
    end
    
  rescue StandardError => e
    Honeybadger.notify(e, context: { user_poster_layout_id: user_poster_layout_id })
    Rails.logger.error("GenerateUserPosterContentWorker failed for layout #{user_poster_layout_id}: #{e.message}")
    raise
  end

  private

  def generate_protocol_photo(user)
    # Use existing GenerateProtocolImageWorker pattern with AWS Lambda
    user_poster_layout = UserPosterLayout.find_by(entity: user)
    return nil if user_poster_layout.blank?

    bearer_token = JsonWebToken.get_token_for_media_to_ror_auth
    payload = {
      user_id: user.id,
      bearer_token: bearer_token
    }

    begin
      # Invoke AWS Lambda for protocol photo generation
      payload_response = invoke_lambda(payload)

      # Parse the response from lambda
      payload_parsed_response = JSON.parse(payload_response.payload.read)

      if payload_parsed_response['statusCode'] != 200
        Rails.logger.error("Lambda returned non-200 status for user #{user.id}: #{payload_parsed_response}")
        return nil
      end

      response_body = JSON.parse(payload_parsed_response['body'])
      protocol_url = response_body['protocol_url']

      unless protocol_url.present?
        Rails.logger.error("Missing protocol_url in lambda response for user #{user.id}: #{response_body}")
        return nil
      end

      return protocol_url

    rescue StandardError => e
      Rails.logger.error("Protocol photo generation error for user #{user.id}: #{e.message}")
      Honeybadger.notify(e, context: { user_id: user.id })
      return nil
    end
  end

  def invoke_lambda(payload)
    Aws::Lambda::Client.new(
      region: 'ap-south-1',
      credentials: Aws::Credentials.new(
        Rails.application.credentials[:aws_access_key_id],
        Rails.application.credentials[:aws_secret_access_key]
      )
    ).invoke(
      function_name: 'arn:aws:lambda:ap-south-1:666527360739:function:UserProtocolPhotoGenerati-UserProtocolPhotoGenerat-t8TQt2zcsqvh',
      invocation_type: 'RequestResponse',
      payload: payload.to_json
    )
  end

  def generate_user_video_frame_with_identity(user, video_frame)
    # Generate identity photo for this video frame
    identity_image_url = generate_identity_image(user, video_frame)
    return nil if identity_image_url.blank?

    # Check if UserVideoFrame already exists (handle unique constraint)
    existing_frame = UserVideoFrame.find_by(user: user, video_frame: video_frame, active: true)

    if existing_frame.present?
      # Update existing frame with new identity photo URL
      existing_frame.update!(identity_photo_url: identity_image_url)
      Rails.logger.info("Updated existing UserVideoFrame #{existing_frame.id} for user #{user.id}, video_frame #{video_frame.id}")
      return existing_frame
    else
      # Create new user video frame with zero-downtime approach
      user_video_frame = UserVideoFrame.create!(
        user: user,
        video_frame: video_frame,
        identity_photo_url: identity_image_url,
        active: true
      )

      Rails.logger.info("Created UserVideoFrame #{user_video_frame.id} for user #{user.id}, video_frame #{video_frame.id}")
      return user_video_frame
    end

  rescue StandardError => e
    Rails.logger.error("Failed to create/update UserVideoFrame for user #{user.id}, video_frame #{video_frame.id}: #{e.message}")
    raise
  end

  def generate_identity_image(user, video_frame)
    # Get user data for identity image
    user_name = user.name
    badge_description = user.get_badge_role&.get_description
    
    # Get font information from video frame
    font = video_frame.font
    name_font_family = font.name_font
    badge_font_family = font.badge_font
    
    # Generate HTML content for all three modes
    modes = [:PORTRAIT, :LANDSCAPE, :SQUARE]
    
    # Generate for the specific video frame mode
    video_type = video_frame.video_type.to_sym
    return nil unless modes.include?(video_type)
    
    html = generate_html(
      template_name: 'identity_photo_template',
      video_type: video_type,
      user_name: user_name,
      badge_description: badge_description,
      name_font_family: name_font_family,
      badge_font_family: badge_font_family
    )
    
    # Capture HTML as image
    uploaded_image = Capture::capture_html_as_image(html, '#top-outer-container')
    raise "Did not receive url from captured html - #{uploaded_image}" unless uploaded_image['cdn_url'].present?

    uploaded_image['cdn_url']
  end

  def generate_html(template_name:, video_type:, user_name:, badge_description:, name_font_family:, badge_font_family:)
    # Get dimensions and styling based on video type
    dimensions = UserVideoFrame::IDENTITY_IMAGE_DIMENSIONS[video_type.to_s]
    user_photo_dimensions = UserVideoFrame::USER_PHOTO_DIMENSIONS[video_type.to_s]

    # Calculate font sizes based on name length with improved logic
    name_font_size = calculate_name_font_size(user_name)

    # Determine user photo width based on video type (0px for portrait, actual width for landscape)
    user_photo_width = video_type == :PORTRAIT ? 0 : user_photo_dimensions[:width] + 70

    locals = {
      container_width: dimensions[:width] * 2,
      container_height: dimensions[:height] * 2,
      text_padding: UserVideoFrame::IDENTITY_TEXT_PADDING,
      name_font_size: name_font_size,
      badge_font_size: UserVideoFrame::IDENTITY_BADGE_FONT_SIZE,
      name_font_family: name_font_family,
      badge_font_family: badge_font_family,
      user_name: user_name,
      badge_description: badge_description,
      user_photo_width: user_photo_width,
      left_padding: UserVideoFrame::IDENTITY_LEFT_PADDING_LANDSCAPE
    }

    ActionController::Base.render(
      inline: File.read(Rails.root.join('app', 'views', "#{template_name}.erb")),
      locals: locals
    )
  end

  def calculate_name_font_size(user_name)
    # Implement font size calculation based on name length
    # Following existing pattern from IdentityPhotoGenerationWorker
    name_length = user_name.length
    
    case name_length
    when 0..10
      UserVideoFrame::IDENTITY_NAME_FONT_SIZE_LARGE
    when 11..15
      UserVideoFrame::IDENTITY_NAME_FONT_SIZE_MEDIUM
    else
      UserVideoFrame::IDENTITY_NAME_FONT_SIZE_SMALL
    end
  end


end
