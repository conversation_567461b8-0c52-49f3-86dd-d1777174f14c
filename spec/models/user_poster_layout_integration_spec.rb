# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UserPosterLayout, type: :model do
  describe 'poster content generation integration' do
    let(:user) { FactoryBot.create(:user) }
    let(:font) { FactoryBot.create(:font, name_font: "Noto Sans Telugu", badge_font: "Noto Sans Telugu") }
    let!(:video_frame_portrait) { FactoryBot.create(:video_frame, video_type: 'PORTRAIT', font: font, active: true) }
    let!(:video_frame_landscape) { FactoryBot.create(:video_frame, video_type: 'LANDSCAPE', font: font, active: true) }

    before do
      # Mock Sidekiq to track job enqueuing
      allow(GenerateUserPosterContentWorker).to receive(:perform_async)
    end

    context 'when creating a user poster layout' do
      it 'queues the poster content generation worker' do
        user_poster_layout = FactoryBot.build(:user_poster_layout, entity: user)
        
        expect(GenerateUserPosterContentWorker).to receive(:perform_async).with(anything)
        
        user_poster_layout.save!
      end

      it 'logs the queuing action' do
        user_poster_layout = FactoryBot.build(:user_poster_layout, entity: user)
        
        expect(Rails.logger).to receive(:info).with(/Queued poster content generation for user #{user.id}/)
        
        user_poster_layout.save!
      end
    end

    context 'when creating a circle poster layout' do
      let(:circle) { FactoryBot.create(:circle) }

      it 'does not queue the worker for circle entities' do
        circle_layout = FactoryBot.build(:user_poster_layout, entity: circle)
        
        expect(GenerateUserPosterContentWorker).not_to receive(:perform_async)
        
        circle_layout.save!
      end
    end

    context 'when updating an existing poster layout' do
      let!(:user_poster_layout) { FactoryBot.create(:user_poster_layout, entity: user) }

      it 'does not queue the worker on updates' do
        expect(GenerateUserPosterContentWorker).not_to receive(:perform_async)
        
        user_poster_layout.update!(h1_count: 2)
      end
    end
  end

  describe '#queue_poster_content_generation' do
    let(:user) { FactoryBot.create(:user) }
    let(:circle) { FactoryBot.create(:circle) }

    context 'with user entity' do
      let(:user_poster_layout) { FactoryBot.create(:user_poster_layout, entity: user) }

      it 'queues the worker with layout id' do
        expect(GenerateUserPosterContentWorker).to receive(:perform_async).with(user_poster_layout.id)
        
        user_poster_layout.send(:queue_poster_content_generation)
      end
    end

    context 'with circle entity' do
      let(:circle_layout) { FactoryBot.create(:user_poster_layout, entity: circle) }

      it 'does not queue the worker' do
        expect(GenerateUserPosterContentWorker).not_to receive(:perform_async)
        
        circle_layout.send(:queue_poster_content_generation)
      end
    end
  end
end
