# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GenerateUserPosterContentWorker, type: :worker do
  let(:user) { FactoryBot.create(:user) }
  let(:user_poster_layout) { FactoryBot.create(:user_poster_layout, entity: user) }
  let(:font) { Font.find_by(name_font: "Noto Sans Telugu", badge_font: "Noto Sans Telugu") || FactoryBot.create(:font, name_font: "Noto Sans Telugu #{SecureRandom.hex(4)}", badge_font: "Noto Sans Telugu #{SecureRandom.hex(4)}") }
  let(:video_frame_portrait) { FactoryBot.create(:video_frame, video_type: 'PORTRAIT', font: font, active: true) }
  let(:video_frame_landscape) { FactoryBot.create(:video_frame, video_type: 'LANDSCAPE', font: font, active: true) }
  let(:video_frame_square) { FactoryBot.create(:video_frame, video_type: 'SQUARE', font: font, active: true) }
  let(:mock_lambda_client) { instance_double(Aws::Lambda::Client) }
  let(:protocol_url) { 'https://a-cdn.thecircleapp.in/protocol/test_protocol.jpg' }
  let(:identity_image_url) { 'https://a-cdn.thecircleapp.in/identity/test_identity.jpg' }

  subject { described_class.new }

  before do
    allow(Aws::Lambda::Client).to receive(:new).and_return(mock_lambda_client)
    allow(Capture).to receive(:capture_html_as_image).and_return({ 'cdn_url' => identity_image_url })
  end

  describe '#perform' do
    context 'when user poster layout does not exist' do
      it 'returns early without processing' do
        expect(Rails.logger).not_to receive(:info)
        subject.perform(999999)
      end
    end

    context 'when entity is not a User' do
      let(:circle) { FactoryBot.create(:circle) }
      let(:circle_layout) { FactoryBot.create(:user_poster_layout, entity: circle) }

      it 'returns early without processing' do
        expect(Rails.logger).not_to receive(:info)
        subject.perform(circle_layout.id)
      end
    end

    context 'when no active video frames exist' do
      before do
        VideoFrame.update_all(active: false)
      end

      it 'returns early without processing' do
        expect(Rails.logger).to receive(:info).with("Starting poster content generation for user #{user.id}, layout #{user_poster_layout.id}")
        subject.perform(user_poster_layout.id)
      end
    end

    context 'with valid user poster layout and active video frames' do
      let(:lambda_response_body) do
        {
          'statusCode' => 200,
          'body' => { 'protocol_url' => protocol_url }.to_json
        }
      end
      let(:lambda_response) { double('response', payload: double('payload', read: lambda_response_body.to_json)) }

      before do
        video_frame_portrait
        video_frame_landscape
        video_frame_square
        
        allow(mock_lambda_client).to receive(:invoke).and_return(lambda_response)
      end

      it 'generates protocol photo and updates poster layout' do
        expect(mock_lambda_client).to receive(:invoke).with(
          function_name: 'arn:aws:lambda:ap-south-1:666527360739:function:UserProtocolPhotoGenerati-UserProtocolPhotoGenerat-t8TQt2zcsqvh',
          invocation_type: 'RequestResponse',
          payload: {
            user_id: user.id,
            bearer_token: JsonWebToken.get_token_for_media_to_ror_auth
          }.to_json
        )

        subject.perform(user_poster_layout.id)

        user_poster_layout.reload
        expect(user_poster_layout.video_frame_protocol_photo_url).to eq(protocol_url)
      end

      it 'creates user video frames for all active video frame types' do
        expect {
          subject.perform(user_poster_layout.id)
        }.to change(UserVideoFrame, :count).by(3)

        # Check that frames were created for all three video types
        expect(UserVideoFrame.joins(:video_frame).where(user: user, video_frames: { video_type: 'PORTRAIT' }).count).to eq(1)
        expect(UserVideoFrame.joins(:video_frame).where(user: user, video_frames: { video_type: 'LANDSCAPE' }).count).to eq(1)
        expect(UserVideoFrame.joins(:video_frame).where(user: user, video_frames: { video_type: 'SQUARE' }).count).to eq(1)
      end

      it 'generates identity photos for each video frame' do
        expect(Capture).to receive(:capture_html_as_image).exactly(3).times.and_return({ 'cdn_url' => identity_image_url })

        subject.perform(user_poster_layout.id)

        UserVideoFrame.where(user: user).each do |frame|
          expect(frame.identity_photo_url).to eq(identity_image_url)
        end
      end

      it 'logs successful generation' do
        expect(Rails.logger).to receive(:info).with("Starting poster content generation for user #{user.id}, layout #{user_poster_layout.id}")
        expect(Rails.logger).to receive(:info).with(/Generated content for user #{user.id}, video_frame/).exactly(3).times
        expect(Rails.logger).to receive(:info).with("Successfully generated/updated 3 video frames for user #{user.id}")

        subject.perform(user_poster_layout.id)
      end

      context 'when user video frames already exist' do
        let!(:existing_frame) { FactoryBot.create(:user_video_frame, user: user, video_frame: video_frame_portrait, active: true) }

        it 'updates existing frames instead of creating new ones' do
          expect {
            subject.perform(user_poster_layout.id)
          }.to change(UserVideoFrame, :count).by(2) # Only 2 new frames, 1 updated

          existing_frame.reload
          expect(existing_frame.identity_photo_url).to eq(identity_image_url)
        end
      end

      context 'when protocol photo generation fails' do
        let(:failed_lambda_response_body) do
          {
            'statusCode' => 500,
            'body' => { 'error' => 'Lambda failed' }.to_json
          }
        end
        let(:failed_lambda_response) { double('response', payload: double('payload', read: failed_lambda_response_body.to_json)) }

        before do
          allow(mock_lambda_client).to receive(:invoke).and_return(failed_lambda_response)
        end

        it 'continues with identity photo generation even if protocol fails' do
          expect(Rails.logger).to receive(:error).with(/Lambda returned non-200 status for user #{user.id}/)

          expect {
            subject.perform(user_poster_layout.id)
          }.to change(UserVideoFrame, :count).by(3)

          user_poster_layout.reload
          expect(user_poster_layout.video_frame_protocol_photo_url).to be_nil
        end
      end

      context 'when identity photo generation fails for one frame' do
        before do
          allow(Capture).to receive(:capture_html_as_image).and_return({ 'cdn_url' => nil })
        end

        it 'logs error and continues with other frames' do
          expect(Honeybadger).to receive(:notify).exactly(3).times
          expect(Rails.logger).to receive(:error).with(/Failed to generate content for user #{user.id}/).exactly(3).times

          expect {
            subject.perform(user_poster_layout.id)
          }.not_to change(UserVideoFrame, :count)
        end
      end
    end
  end

  describe '#generate_identity_image' do
    let(:role) { FactoryBot.create(:role, has_badge: true) }
    let(:user_role) { FactoryBot.create(:user_role, user: user, role: role, verification_status: 'verified', primary_role: true) }

    before do
      user_role # Create the user role
      allow(role).to receive(:get_description).and_return('Test Badge Description')
    end

    it 'generates HTML with correct parameters for PORTRAIT mode' do
      expect(subject.send(:generate_identity_image, user, video_frame_portrait)).to eq(identity_image_url)
    end

    it 'generates HTML with correct parameters for LANDSCAPE mode' do
      expect(subject.send(:generate_identity_image, user, video_frame_landscape)).to eq(identity_image_url)
    end

    it 'generates HTML with correct parameters for SQUARE mode' do
      expect(subject.send(:generate_identity_image, user, video_frame_square)).to eq(identity_image_url)
    end

    context 'when capture fails' do
      before do
        allow(Capture).to receive(:capture_html_as_image).and_return({ 'cdn_url' => nil })
      end

      it 'raises an error' do
        expect {
          subject.send(:generate_identity_image, user, video_frame_portrait)
        }.to raise_error(RuntimeError, /Did not receive url from captured html/)
      end
    end
  end

  describe '#calculate_name_font_size' do
    it 'returns large font size for short names' do
      expect(subject.send(:calculate_name_font_size, 'John')).to eq(UserVideoFrame::IDENTITY_NAME_FONT_SIZE_LARGE)
    end

    it 'returns medium font size for medium names' do
      expect(subject.send(:calculate_name_font_size, 'John Doe Smith')).to eq(UserVideoFrame::IDENTITY_NAME_FONT_SIZE_MEDIUM)
    end

    it 'returns small font size for long names' do
      expect(subject.send(:calculate_name_font_size, 'Very Long Name That Exceeds Limit')).to eq(UserVideoFrame::IDENTITY_NAME_FONT_SIZE_SMALL)
    end
  end
end
