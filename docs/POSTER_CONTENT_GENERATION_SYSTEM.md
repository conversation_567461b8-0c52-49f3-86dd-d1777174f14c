# Comprehensive User Poster Content Generation System

## Overview

This document describes the implementation of a comprehensive Sidekiq background worker system that automatically generates user video frames, identity photos, and protocol photos when a new user poster layout is created.

## Architecture

### Core Components

1. **GenerateUserPosterContentWorker** - Main Sidekiq worker for comprehensive content generation
2. **UserPosterLayout Model** - Enhanced with after_create callback to trigger worker
3. **UserVideoFrame Model** - Stores generated video frames with identity photos
4. **Capture Module** - Handles HTML-to-image generation using media service
5. **AWS Lambda Integration** - Protocol photo generation via existing Lambda function

### Data Flow

```
UserPosterLayout Creation
    ↓
after_create_commit callback
    ↓
GenerateUserPosterContentWorker.perform_async(layout_id)
    ↓
┌─────────────────────────────────────────────────────────┐
│ Worker Processing:                                      │
│ 1. Generate protocol photo (AWS Lambda)                │
│ 2. For each active VideoFrame:                         │
│    - Generate identity photo (3 modes: P/L/S)          │
│    - Create/Update UserVideoFrame                      │
│ 3. Update poster layout with protocol photo URL        │
└─────────────────────────────────────────────────────────┘
```

## Implementation Details

### 1. Worker Configuration

```ruby
class GenerateUserPosterContentWorker
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker
  include Capture
  
  sidekiq_options queue: :video_posters_generation, retry: 3, 
                  lock: :until_and_while_executing, on_conflict: :log
```

**Key Features:**
- Uses `:video_posters_generation` queue for consistency with existing workers
- Implements retry mechanism with 3 attempts
- Prevents concurrent execution for same job
- Includes comprehensive error handling with Honeybadger integration

### 2. Protocol Photo Generation

The worker integrates with the existing AWS Lambda function for protocol photo generation:

```ruby
def generate_protocol_photo(user)
  # Uses same Lambda ARN as GenerateProtocolImageWorker
  # arn:aws:lambda:ap-south-1:666527360739:function:UserProtocolPhotoGenerati-UserProtocolPhotoGenerat-t8TQt2zcsqvh
end
```

**Features:**
- Follows existing GenerateProtocolImageWorker patterns
- Handles Lambda failures gracefully
- Updates UserPosterLayout.video_frame_protocol_photo_url

### 3. Identity Photo Generation

Generates identity photos for all three video frame modes:

- **Portrait Mode**: 0px width CSS variable (text-only layout)
- **Landscape Mode**: Actual width CSS variable (includes user photo space)
- **Square Mode**: Similar to landscape with different dimensions

**Template Integration:**
- Uses existing `identity_photo_template.erb`
- Dynamic CSS variables for responsive sizing
- Font size calculation based on name length
- Proper ellipsis overflow handling

### 4. Zero-Downtime Approach

The system implements a zero-downtime approach:

1. **Check for existing UserVideoFrame** with unique constraint `[user_id, video_frame_id, active]`
2. **Update existing** or **create new** UserVideoFrame
3. **No deletion** of old frames during generation
4. **Continuous availability** of user content

### 5. Database Schema Integration

**UserVideoFrame Table:**
```sql
CREATE TABLE user_video_frames (
  user_id BIGINT NOT NULL,
  video_frame_id BIGINT NOT NULL,
  identity_photo_url TEXT NOT NULL,
  active BOOLEAN DEFAULT TRUE,
  UNIQUE INDEX (user_id, video_frame_id, active)
);
```

**Relationships:**
- `UserVideoFrame` belongs_to `User` and `VideoFrame`
- `UserVideoFrame` has_many `UserVideoPoster`
- `UserPosterLayout` stores `video_frame_protocol_photo_url`

## Usage

### Automatic Triggering

The system automatically triggers when creating a new UserPosterLayout:

```ruby
user_poster_layout = UserPosterLayout.create!(
  entity: user,
  h1_count: 1,
  h2_count: 2,
  active: true
)
# Worker automatically queued via after_create_commit callback
```

### Manual Triggering

For manual execution or testing:

```ruby
GenerateUserPosterContentWorker.perform_async(user_poster_layout.id)
```

### Monitoring

Monitor job execution through:
- **Sidekiq Dashboard**: Queue status and job monitoring
- **Rails Logs**: Comprehensive logging at each step
- **Honeybadger**: Error tracking and context

## Error Handling

### Comprehensive Error Recovery

1. **Protocol Photo Failures**: Continue with identity photo generation
2. **Individual Frame Failures**: Log error, continue with other frames
3. **Capture Service Failures**: Retry mechanism with exponential backoff
4. **Database Constraint Violations**: Handle gracefully with update logic

### Logging Strategy

```ruby
# Success logging
Rails.logger.info("Starting poster content generation for user #{user.id}")
Rails.logger.info("Generated content for user #{user.id}, video_frame #{video_frame.id}")

# Error logging with context
Rails.logger.error("Failed to generate content for user #{user.id}: #{e.message}")
Honeybadger.notify(e, context: { user_id: user.id, video_frame_id: video_frame.id })
```

## Testing

### Comprehensive Test Coverage

1. **Worker Unit Tests**: `spec/workers/generate_user_poster_content_worker_spec.rb`
   - Protocol photo generation
   - Identity photo generation for all modes
   - Error handling scenarios
   - Zero-downtime behavior

2. **Integration Tests**: `spec/models/user_poster_layout_integration_spec.rb`
   - Callback triggering
   - User vs Circle entity handling
   - Job queuing verification

### Test Execution

```bash
# Run worker tests
rspec spec/workers/generate_user_poster_content_worker_spec.rb

# Run integration tests
rspec spec/models/user_poster_layout_integration_spec.rb

# Run all related tests
rspec spec/workers/ spec/models/user_poster_layout*
```

## Performance Considerations

### Optimization Features

1. **Batch Processing**: Processes all video frames in single job
2. **Efficient Queries**: Minimal database queries with proper indexing
3. **Concurrent Safety**: Lock mechanism prevents duplicate processing
4. **Resource Management**: Proper cleanup and error boundaries

### Scalability

- **Queue Isolation**: Uses dedicated `:video_posters_generation` queue
- **Throttling**: Sidekiq::Throttled integration for rate limiting
- **Monitoring**: Built-in job monitoring and retry mechanisms

## Compatibility

### Existing System Integration

- **RegenerateUserIdentityImagesWorker**: Compatible patterns and queue usage
- **IdentityPhotoGenerationWorker**: Reuses template and capture patterns
- **GenerateProtocolImageWorker**: Same AWS Lambda integration
- **Capture Module**: Full compatibility with existing ERB templates

### Migration Safety

- **Non-breaking**: Existing functionality remains unchanged
- **Additive**: Only adds new functionality, no modifications to existing code
- **Backward Compatible**: Works with existing UserVideoFrame records

## Maintenance

### Regular Monitoring

1. **Queue Health**: Monitor `:video_posters_generation` queue
2. **Error Rates**: Track Honeybadger notifications
3. **Performance**: Monitor job execution times
4. **Resource Usage**: AWS Lambda invocation costs

### Troubleshooting

Common issues and solutions:

1. **Lambda Timeouts**: Check AWS Lambda logs and increase timeout if needed
2. **Capture Service Failures**: Verify media service availability
3. **Database Constraints**: Check for unique constraint violations
4. **Queue Backlog**: Scale Sidekiq workers if needed

## Future Enhancements

### Potential Improvements

1. **Batch Protocol Generation**: Generate protocols for multiple users
2. **Caching Layer**: Cache generated identity photos for similar users
3. **Progressive Enhancement**: Generate high-priority frames first
4. **Analytics Integration**: Track generation success rates and performance

### Extension Points

- **Custom Templates**: Support for additional ERB templates
- **Video Frame Types**: Easy addition of new video frame modes
- **Generation Strategies**: Pluggable generation algorithms
- **Notification System**: User notifications for completion
